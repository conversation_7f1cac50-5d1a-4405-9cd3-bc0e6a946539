# 🎯 AI-Powered Quiz Maker

An intelligent quiz generation and assessment system powered by Google's Gemini AI that creates dynamic, personalized quizzes on any topic with instant feedback and performance analysis.

![Python](https://img.shields.io/badge/python-v3.11+-blue.svg)
![Gemini](https://img.shields.io/badge/AI-Gemini%202.0%20Flash-orange.svg)
![Streamlit](https://img.shields.io/badge/UI-Streamlit-red.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

## 🌟 Features

### 🤖 **AI-Powered Quiz Generation**
- Generate quizzes on **any topic** using Google's Gemini 2.0 Flash model
- Customizable **difficulty levels** (Easy, Medium, Hard)
- **Variable question count** (1-50+ questions)
- **Multiple-choice format** with 4 options per question

### 🎮 **Interactive Experience**
- **Two interfaces**: Command-line and beautiful web UI
- **Randomized answer options** to prevent pattern memorization
- **Real-time scoring** and immediate feedback
- **Progress tracking** throughout the quiz

### 🧠 **Intelligent Performance Analysis**
- **AI-powered review agent** analyzes your performance
- **Personalized feedback** with emojis and encouragement
- **Weak area identification** - pinpoints specific sub-topics to review
- **Constructive suggestions** for improvement

### 🛡️ **Robust Architecture**
- **Multi-agent system** with specialized AI agents
- **Pydantic validation** ensures reliable data structures
- **Error handling** with graceful fallbacks
- **Async processing** for optimal performance

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Google Gemini API key

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/asadullah48/quiz-maker.git
cd quiz-maker
```

2. **Install dependencies using UV** (recommended)
```bash
# Install UV if you haven't already
pip install uv

# Install project dependencies
uv sync
```

Or using pip:
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**
```bash
# Create .env file
echo "GEMINI_API_KEY=your_gemini_api_key_here" > .env
```

Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

### Usage

#### 🖥️ **Command Line Interface**
```bash
python main.py
```

#### 🌐 **Web Interface** (Recommended)
```bash
streamlit run hello.py
```

Then open your browser to `http://localhost:8501`

## 📱 Screenshots

### Web Interface
The Streamlit web interface provides an intuitive, user-friendly experience:

- **Quiz Setup**: Choose topic, number of questions, and difficulty
- **Interactive Questions**: Clean, randomized multiple-choice interface
- **Performance Review**: AI-generated feedback with improvement suggestions

### Command Line Interface
Perfect for developers who prefer terminal-based interactions.

## 🏗️ Project Structure

```
quiz-maker/
├── main.py                     # CLI application
├── hello.py                    # Streamlit web application
├── pyproject.toml             # Project configuration
├── uv.lock                    # Dependency lock file
├── demo code for quiz preparation/  # Example implementations
│   ├── basicconfig.py         # Basic agent configuration
│   ├── agentstools.py         # Multi-agent examples
│   ├── handoff.py            # Agent handoff patterns
│   ├── InputGuardrail.py     # Input validation
│   └── ...                   # More examples
└── README.md                 # This file
```

## 🤖 Architecture

### Multi-Agent System

The application uses a sophisticated **agent-based architecture**:

#### **Quiz Generator Agent**
- Specializes in creating multiple-choice questions
- Follows strict Pydantic schemas for consistent output
- Adapts to different topics and difficulty levels

#### **Quiz Review Agent**
- Analyzes user performance patterns
- Identifies weak subject areas
- Provides personalized feedback and encouragement

### Data Models

```python
class QuizQuestion(BaseModel):
    question: str
    options: List[str]  # Exactly 4 options
    answer: str         # Must be one of the options

class ReviewOutput(BaseModel):
    overall_remark: str      # Performance summary with emoji
    weak_sub_topics: List[str]  # Areas needing improvement
    encouragement: str       # Motivational message
```

## 🔧 Configuration

### Environment Variables
- `GEMINI_API_KEY`: Your Google Gemini API key (required)

### Model Settings
The project uses Google's Gemini 2.0 Flash model via OpenAI-compatible API:

```python
model = OpenAIChatCompletionsModel(
    model="gemini-2.0-flash",
    openai_client=client,
)
```

## 📚 Example Usage

### Creating a Python Programming Quiz
```
Topic: Python programming
Questions: 10
Difficulty: Medium

Generated questions cover:
- Data structures (lists, dictionaries)
- Control flow (loops, conditionals)
- Functions and scope
- Object-oriented programming
- Error handling
```

### Performance Review Example
```
🎉 Great job! You scored 8/10!

Areas to review:
- Python list comprehensions
- Exception handling best practices

Keep up the excellent work! You're mastering Python fundamentals. 💪
```

## 🛠️ Development

### Adding New Features

The modular architecture makes it easy to extend:

1. **New Question Types**: Modify the `QuizQuestion` model
2. **Additional AI Agents**: Create specialized agents for different tasks
3. **Enhanced UI**: Extend the Streamlit interface
4. **New Integrations**: Add support for other AI models

### Demo Code

Explore the `demo code for quiz preparation/` directory for examples of:
- Agent handoffs between multiple AI agents
- Function tools for extending capabilities
- Tracing and monitoring with Langfuse/AgentOps
- Input validation and guardrails

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google Gemini AI** for powerful language model capabilities
- **OpenAI Agents** library for agent framework
- **Streamlit** for beautiful web interface
- **Pydantic** for data validation

## 📞 Contact

**Asadullah Shafique**
- GitHub: [@asadullah48](https://github.com/asadullah48)
- Repository: [quiz-maker](https://github.com/asadullah48/quiz-maker)

---

⭐ **Star this repository if you found it helpful!**